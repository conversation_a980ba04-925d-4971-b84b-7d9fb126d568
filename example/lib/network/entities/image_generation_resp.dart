/// images : [{"url":"https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250813%2F3lopbbccri.jpeg?Expires=1755071902&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=m253Gq75S7ILsKZIlTAdVtSx3MM%3D"}]
/// timings : {"inference":0.338}
/// seed : 1752628817
/// shared_id : "0"
/// data : [{"url":"https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250813%2F3lopbbccri.jpeg?Expires=1755071902&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=m253Gq75S7ILsKZIlTAdVtSx3MM%3D"}]
/// created : 1755068302
class ImageGenerationResp {
  ImageGenerationResp({
    this.images,
    this.timings,
    this.seed,
    this.sharedId,
    this.data,
    this.created,
  });

  ImageGenerationResp.fromJson(dynamic json) {
    if (json['images'] != null) {
      images = [];
      json['images'].forEach((v) {
        images?.add(Images.fromJson(v));
      });
    }
    timings =
        json['timings'] != null ? Timings.fromJson(json['timings']) : null;
    seed = json['seed'];
    sharedId = json['shared_id'];
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(Data.fromJson(v));
      });
    }
    created = json['created'];
  }

  List<Images>? images;
  Timings? timings;
  num? seed;
  String? sharedId;
  List<Data>? data;
  num? created;

  ImageGenerationResp copyWith({
    List<Images>? images,
    Timings? timings,
    num? seed,
    String? sharedId,
    List<Data>? data,
    num? created,
  }) =>
      ImageGenerationResp(
        images: images ?? this.images,
        timings: timings ?? this.timings,
        seed: seed ?? this.seed,
        sharedId: sharedId ?? this.sharedId,
        data: data ?? this.data,
        created: created ?? this.created,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (images != null) {
      map['images'] = images?.map((v) => v.toJson()).toList();
    }
    if (timings != null) {
      map['timings'] = timings?.toJson();
    }
    map['seed'] = seed;
    map['shared_id'] = sharedId;
    if (data != null) {
      map['data'] = data?.map((v) => v.toJson()).toList();
    }
    map['created'] = created;
    return map;
  }
}

/// url : "https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250813%2F3lopbbccri.jpeg?Expires=1755071902&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=m253Gq75S7ILsKZIlTAdVtSx3MM%3D"

class Data {
  Data({
    this.url,
  });

  Data.fromJson(dynamic json) {
    url = json['url'];
  }

  String? url;

  Data copyWith({
    String? url,
  }) =>
      Data(
        url: url ?? this.url,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['url'] = url;
    return map;
  }
}

/// inference : 0.338

class Timings {
  Timings({
    this.inference,
  });

  Timings.fromJson(dynamic json) {
    inference = json['inference'];
  }

  num? inference;

  Timings copyWith({
    num? inference,
  }) =>
      Timings(
        inference: inference ?? this.inference,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['inference'] = inference;
    return map;
  }
}

/// url : "https://sc-maas.oss-cn-shanghai.aliyuncs.com/outputs%2F20250813%2F3lopbbccri.jpeg?Expires=1755071902&OSSAccessKeyId=LTAI5tQnPSzwAnR8NmMzoQq4&Signature=m253Gq75S7ILsKZIlTAdVtSx3MM%3D"

class Images {
  Images({
    this.url,
  });

  Images.fromJson(dynamic json) {
    url = json['url'];
  }

  String? url;

  Images copyWith({
    String? url,
  }) =>
      Images(
        url: url ?? this.url,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['url'] = url;
    return map;
  }
}
