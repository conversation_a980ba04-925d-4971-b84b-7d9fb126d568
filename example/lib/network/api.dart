import 'dart:convert';

import 'package:http/http.dart' as http;

import 'entities/chat_completion_resp.dart';
import 'entities/image_generation_resp.dart';

class Api {

  static const String _baseUrl = 'https://api.siliconflow.cn/v1';
  static const String _apiKey = 'sk-tgjkyezhlaoopfckhnpymngprlhongchkweqlcipdogvmsfe';

  static final Api _instance = Api._internal();

  factory Api() {
    return _instance;
  }

  Api._internal();

  Future<ChatCompletionResp> chatCompletion(String content) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/chat/completions'),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'model': 'Qwen/Qwen2.5-7B-Instruct',
        'messages': [
          {'role': 'user', 'content': content},
        ],
      }),
    );

    if (response.statusCode == 200) {
      return ChatCompletionResp.fromJson(jsonDecode(response.body) as Map<String, dynamic>);
    } else {
      throw Exception('Failed to generate image: ${response.statusCode}, ${response.body}');
    }
  }

  Future<ImageGenerationResp> imagesGenerations(String prompt) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/images/generations'),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'model': 'Kwai-Kolors/Kolors',
        'prompt': prompt,
        'image_size': "512x512",
        'batch_size': 1,
        "num_inference_steps": 20,
        "guidance_scale": 7.5
      }),
    );

    if (response.statusCode == 200) {
      return ImageGenerationResp.fromJson(jsonDecode(response.body) as Map<String, dynamic>);
    } else {
      throw Exception('Failed to generate image: ${response.statusCode}, ${response.body}');
    }
  }
}
